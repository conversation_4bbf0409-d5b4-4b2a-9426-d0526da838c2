// src/lib/server/db/schema/definitions.ts
// CipherX TMA - 数据字典与内容模板表定义

import { pgTable, text } from 'drizzle-orm/pg-core';
import { kinkDefinitionTypeEnum } from './_enums';

/**
 * Kink 定义表 - Kink 数据字典
 * 存储所有 Kink 角色和兴趣的标准化定义、多语言解释和安全警告。
 * 是应用内 Onboarding 和展示 Kink 详情时的内容来源。
 */
export const kinkDefinitions = pgTable('kink_definitions', {
	// id 将直接使用 constants/kinks.ts 中定义的程序化 key，如 'DOMINANT' 或 'rope_play'
	id: text('id').primaryKey(),

	type: kinkDefinitionTypeEnum('type').notNull(),

	// 多语言的显示名称（备用，通常由 Paraglide 处理，但此处可作为后台管理的显示名）
	nameEn: text('name_en').notNull(),
	nameZh: text('name_zh'),

	// 多语言的详细解释
	descriptionEn: text('description_en'),
	descriptionZh: text('description_zh'),

	// 多语言的安全警告或提示
	warningEn: text('warning_en'),
	warningZh: text('warning_zh')
});

/**
 * 内容模板表 - 动态运营内容的“稿件库”
 * 存储由机器人或系统发送的、支持多语言的、可复用的消息模板。
 */
export const contentTemplates = pgTable('content_templates', {
	// 模板的唯一标识符，由程序调用
	id: text('id').primaryKey(), // e.g., 'welcome_new_group', 'subscription_reminder'

	// 在后台给运营人员看，解释这个模板用在何处
	description: text('description'),

	// 为每一种支持的语言创建一个模板内容字段
	// 模板中可使用 {placeholder} 语法，由后端程序动态替换
	contentEn: text('content_en'),
	contentZh: text('content_zh'),
	contentJa: text('content_ja')
});

// --- 类型导出 ---
export type KinkDefinition = typeof kinkDefinitions.$inferSelect;
export type NewKinkDefinition = typeof kinkDefinitions.$inferInsert;

export type ContentTemplate = typeof contentTemplates.$inferSelect;
export type NewContentTemplate = typeof contentTemplates.$inferInsert;
