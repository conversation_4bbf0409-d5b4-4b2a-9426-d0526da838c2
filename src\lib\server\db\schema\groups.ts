// src/lib/server/db/schema/groups.ts
// CipherX TMA - 群组和群组成员关系表定义 (最终版 v1.1)

import { pgTable, text, bigint, integer, timestamp, primaryKey } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { groupRoleEnum } from './_enums';
import { users } from './users';

/**
 * 群组表 - 社区的核心实体
 */
export const groups = pgTable('groups', {
	// 程序化ID，用于内部关联
	id: text('id').primaryKey(),
	// 人类可读、支持水平扩展的唯一ID
	slug: text('slug').unique().notNull(),
	// 与Telegram连接的桥梁
	telegramChatId: bigint('telegram_chat_id', { mode: 'number' }).unique().notNull(),

	// ✅ 最终决定：保留国际化的显示名称字段，由后端管理
	nameEn: text('name_en').notNull(),
	nameZh: text('name_zh'),
	// 国际化描述，由管理员填写
	descriptionEn: text('description_en'),
	descriptionZh: text('description_zh'),

	// 结构化数据，用于筛选
	countryCode: text('country_code').notNull(),
	provinceCode: text('province_code'), // 国家级群组此字段为 NULL
	theme: text('theme').notNull(), // 'men-seeking-men', etc.

	// 管理信息
	creatorId: text('creator_id')
		.notNull()
		.references(() => users.id),
	memberCount: integer('member_count').notNull().default(0),

	createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow()
});

/**
 * 群组成员关系表
 */
export const groupMemberships = pgTable(
	'group_memberships',
	{
		userId: text('user_id')
			.notNull()
			.references(() => users.id, { onDelete: 'cascade' }),
		groupId: text('group_id')
			.notNull()
			.references(() => groups.id, { onDelete: 'cascade' }),
		role: groupRoleEnum('role').notNull().default('member'),
		subscriptionExpiresAt: timestamp('subscription_expires_at', { withTimezone: true }).notNull(),
		joinedAt: timestamp('joined_at', { withTimezone: true }).notNull().defaultNow()
	},
	(table) => [primaryKey({ columns: [table.userId, table.groupId] })]
);

// --- 关系定义 ---

export const groupsRelations = relations(groups, ({ one, many }) => ({
	creator: one(users, {
		fields: [groups.creatorId],
		references: [users.id]
	}),
	memberships: many(groupMemberships)
}));

export const groupMembershipsRelations = relations(groupMemberships, ({ one }) => ({
	user: one(users, {
		fields: [groupMemberships.userId],
		references: [users.id]
	}),
	group: one(groups, {
		fields: [groupMemberships.groupId],
		references: [groups.id]
	})
}));
