<script lang="ts">
	import { App, Preloader } from 'konsta/svelte';
	import * as m from '$lib/paraglide/messages';
	import { initializeTma, tmaReady } from '$lib/telegram/tma.service';
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';
	import '../app.css';
	import Loader from '$components/ui/Loader.svelte';

	let { children } = $props();

	onMount(() => {
		if (browser) {
			initializeTma();
		}
	});
</script>

<svelte:head>
	<title>{m.app_title()}</title>
	<meta name="description" content="CipherX - A new community platform" />
</svelte:head>

{#if $tmaReady}
	<App theme="ios">
		{@render children()}
	</App>
{:else}
	<Loader />
{/if}
