// src/lib/server/db/schema/_enums.ts
// CipherX TMA - PostgreSQL 枚举类型定义
// 这些枚举必须与 drizzle/0000_init_database.sql 中的定义完全一致

import { pgEnum } from 'drizzle-orm/pg-core';

/**
 * 性取向枚举
 */
export const orientationEnum = pgEnum('orientation_enum', [
  'straight',
  'gay', 
  'lesbian',
  'bisexual',
  'asexual',
  'demisexual',
  'pansexual',
  'queer',
  'fluid',
  'other_orientation',
  'prefer_not_to_say_orientation'
]);

/**
 * 身体类型枚举
 */
export const bodyTypeEnum = pgEnum('body_type_enum', [
  'male_body',
  'female_body', 
  'other_body_type'
]);

/**
 * 外表风格枚举
 */
export const presentationStyleEnum = pgEnum('presentation_style_enum', [
  'conventional_masculine',
  'rugged_masculine',
  'feminine',
  'androgynous_neutral',
  'other_presentation_style'
]);

/**
 * 关系状态枚举
 */
export const relationshipStatusEnum = pgEnum('relationship_status_enum', [
  'single',
  'in_a_relationship',
  'complicated',
  'open_relationship',
  'married',
  'polyamorous',
  'other_relationship_status',
  'prefer_not_to_say_relationship_status'
]);

/**
 * 匹配状态枚举
 */
export const matchStatusEnum = pgEnum('match_status_enum', [
  'liked',
  'matched',
  'blocked'
]);

/**
 * 积分交易类型枚举
 */
export const pointTransactionTypeEnum = pgEnum('point_transaction_type_enum', [
  'registration_bonus',
  'daily_check_in',
  'profile_completion_bonus',
  'group_subscription_fee',
  'super_like_cost',
  'invite_bonus',
  'top_up',
  'system_adjustment'
]);

/**
 * 群组角色枚举
 */
export const groupRoleEnum = pgEnum('group_role_enum', [
  'member',
  'moderator',
  'admin'
]);

/**
 * Kink 定义类型枚举
 */
export const kinkDefinitionTypeEnum = pgEnum('kink_definition_type_enum', [
  'role',
  'interest'
]);

// 导出所有枚举类型，供其他模块使用
export type OrientationType = typeof orientationEnum.enumValues[number];
export type BodyType = typeof bodyTypeEnum.enumValues[number];
export type PresentationStyle = typeof presentationStyleEnum.enumValues[number];
export type RelationshipStatus = typeof relationshipStatusEnum.enumValues[number];
export type MatchStatus = typeof matchStatusEnum.enumValues[number];
export type PointTransactionType = typeof pointTransactionTypeEnum.enumValues[number];
export type GroupRole = typeof groupRoleEnum.enumValues[number];
export type KinkDefinitionType = typeof kinkDefinitionTypeEnum.enumValues[number];
