<!-- src/lib/components/ui/RangeSlider.svelte -->
<script lang="ts">
	import { Range, ListItem, BlockTitle } from 'konsta/svelte';
	import type { ComponentProps } from 'svelte';

	type Props = {
		value?: number;
		label: string;
		min: number;
		max: number;
		step?: number;
		unit?: string;
	} & Omit<ComponentProps<Range>, 'value' | 'min' | 'max' | 'step'>;

	let {
		value = $bindable(0),
		label,
		min,
		max,
		step = 1,
		unit = '',
		...restProps
	}: Props = $props();
</script>

<div>
	<BlockTitle class="flex justify-between">
		<span>{label}</span>
		<span class="font-semibold">{value}{unit}</span>
	</BlockTitle>
	<ListItem innerClass="flex space-x-4 rtl:space-x-reverse">
		<span class="text-sm opacity-50">{min}</span>
		<Range
			{...restProps}
			{min}
			{max}
			{step}
			bind:value
		/>
		<span class="text-sm opacity-50">{max}</span>
	</ListItem>
</div>
