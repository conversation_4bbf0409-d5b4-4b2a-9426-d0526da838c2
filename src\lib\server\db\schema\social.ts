// src/lib/server/db/schema/social.ts
// CipherX TMA - 社交图谱相关表定义

import { pgTable, text, timestamp, primaryKey, check } from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';
import { matchStatusEnum } from './_enums';
import { users } from './users';

/**
 * 匹配表 - 用户间的社交关系 (喜欢、匹配、屏蔽)
 */
export const matches = pgTable(
	'matches',
	{
		// 关键修正点 1: 添加外键约束
		actorUserId: text('actor_user_id')
			.notNull()
			.references(() => users.id, { onDelete: 'cascade' }),

		targetUserId: text('target_user_id')
			.notNull()
			.references(() => users.id, { onDelete: 'cascade' }),

		status: matchStatusEnum('status').notNull(),

		createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow(),
		updatedAt: timestamp('updated_at', { withTimezone: true }).notNull().defaultNow()
	},
	(table) => [
		// 关键修正点 2: 使用数组语法，并添加 CHECK 约束
		primaryKey({ columns: [table.actorUserId, table.targetUserId] }),
		check('chk_no_self_match', sql`${table.actorUserId} <> ${table.targetUserId}`)
	]
);

// --- 关系定义 (Relations) ---
// 关键修正点 3: 将 relations 定义移到本文件中
export const matchesRelations = relations(matches, ({ one }) => ({
	// 定义 actor 关系，用于从 match 记录查询发起者信息
	actor: one(users, {
		fields: [matches.actorUserId],
		references: [users.id],
		relationName: 'match_actor' // 必须与 usersRelations 中的名称匹配
	}),
	// 定义 target 关系，用于从 match 记录查询接收者信息
	target: one(users, {
		fields: [matches.targetUserId],
		references: [users.id],
		relationName: 'match_target' // 必须与 usersRelations 中的名称匹配
	})
}));

// --- 类型导出 ---
export type Match = typeof matches.$inferSelect;
export type NewMatch = typeof matches.$inferInsert;
