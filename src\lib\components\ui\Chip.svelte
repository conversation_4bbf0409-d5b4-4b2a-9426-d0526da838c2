<!-- src/lib/components/ui/Chip.svelte -->
<script lang="ts">
	import { Chip as KonstaChip } from 'konsta/svelte';
	import type { ComponentProps } from 'svelte';

	type Props = {
		value: any;
		label: string;
		selected?: boolean;
		onclick?: (event: MouseEvent) => void;
	} & Omit<ComponentProps<KonstaChip>, 'onClick'>;

	let { value, label, selected = false, onclick, ...restProps }: Props = $props();
</script>

<KonstaChip {...restProps} class="m-0.5 cursor-pointer select-none" outline={!selected} {onclick}>
	{label}
</KonstaChip>
