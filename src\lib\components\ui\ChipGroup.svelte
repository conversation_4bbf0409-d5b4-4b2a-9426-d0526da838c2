<!-- src/lib/components/ui/ChipGroup.svelte -->
<script lang="ts">
	import Chip from './Chip.svelte';

	type Item = {
		value: any;
		label: string;
	};

	type Props = {
		items: Item[];
		value?: any | any[];
		multiple?: boolean;
	};

	let { items, value = $bindable(), multiple = false }: Props = $props();

	function handleClick(itemValue: any) {
		if (multiple) {
			const currentValue = Array.isArray(value) ? [...value] : [];
			const index = currentValue.indexOf(itemValue);
			if (index > -1) {
				currentValue.splice(index, 1);
			} else {
				currentValue.push(itemValue);
			}
			value = currentValue;
		} else {
			value = itemValue;
		}
	}

	function isSelected(itemValue: any): boolean {
		if (multiple) {
			return Array.isArray(value) && value.includes(itemValue);
		}
		return value === itemValue;
	}
</script>

<div class="flex flex-wrap">
	{#each items as item}
		<Chip
			label={item.label}
			value={item.value}
			selected={isSelected(item.value)}
			onclick={() => handleClick(item.value)}
		/>
	{/each}
</div>
