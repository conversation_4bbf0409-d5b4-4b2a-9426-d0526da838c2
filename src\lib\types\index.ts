// src/lib/types/index.ts
// CipherX TMA - 全局共享 TypeScript 类型定义
// 职责：定义所有“纯粹的”、非 Drizzle、非 Zod 的、无法被自动推断的通用类型。

/**
 * UI 状态相关类型
 * 这些类型描述了客户端UI的瞬时状态。
 */
export interface ToastMessage {
	id: string;
	type: 'success' | 'error' | 'warning' | 'info';
	title?: string;
	message: string;
	duration?: number;
}

export interface ModalState {
	isOpen: boolean;
	type?: 'confirm' | 'alert' | 'custom'; // 可以定义模态框的类型
	title?: string;
	content?: string;
	data?: any; // 可选的、传递给模态框组件的数据
}

export interface LoadingState {
	isLoading: boolean;
	message?: string;
}

/**
 * 应用配置与特性开关
 * 这个类型可以由一个配置文件或后端API提供，用于控制应用行为。
 */
export interface AppConfig {
	version: string;
	environment: 'development' | 'staging' | 'production';
	features: {
		tonWallet: boolean;
		advancedSearch: boolean;
		groupCreation: boolean;
	};
}

// 可以在这里保留一些极其通用的、全域性的类型别名
export type TabId = 'discover' | 'growth' | 'interactions' | 'profile';
