// src/lib/server/db/index.ts
// CipherX TMA - 数据库连接和配置 (使用 node-postgres 'pg')

import { drizzle } from 'drizzle-orm/node-postgres'; // 👈 修正点 1: 导入 pg 对应的适配器
import { Pool } from 'pg'; // 👈 修正点 2: 从 'pg' 导入 Pool
import { env } from '$env/dynamic/private';
import * as schema from './schema'; // 导入我们聚合后的 schema 入口

// 1. 检查环境变量
if (!env.DATABASE_URL) {
	throw new Error('FATAL: DATABASE_URL environment variable is not set');
}

// 2. 创建 pg 的连接池
// Pool 是 node-postgres 中管理多个连接的推荐方式
const pool = new Pool({
	connectionString: env.DATABASE_URL
});

// 3. 创建 Drizzle 数据库实例，并传入 pg 的 Pool 和我们完整的 schema
export const db = drizzle(pool, {
	schema,
	logger: process.env.NODE_ENV === 'development' // 开发环境启用日志
});

/**
 * 数据库健康检查函数
 * 用于 /healthcheck 端点或部署平台的健康检查
 */
export async function checkDatabaseHealth(): Promise<{ ok: boolean; error?: string }> {
	try {
		// 从连接池中获取一个客户端来执行一个简单的查询
		const client = await pool.connect();
		await client.query('SELECT 1');
		client.release(); // 将客户端释放回连接池
		return { ok: true };
	} catch (error) {
		console.error('Database health check failed:', error);
		return { ok: false, error: error instanceof Error ? error.message : 'Unknown error' };
	}
}

/**
 * (在应用优雅退出时调用) 关闭所有数据库连接
 */
export async function closeDatabaseConnection(): Promise<void> {
	try {
		await pool.end();
		console.log('Database connection pool closed.');
	} catch (error) {
		console.error('Error closing database connection pool:', error);
	}
}
