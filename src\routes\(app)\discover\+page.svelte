<!-- src/routes/(app)/discover/+page.svelte -->
<script lang="ts">
	import { Page, Navbar, Block, Button, BlockTitle, Card } from 'konsta/svelte';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();
</script>

<Page>
	<Navbar title="Discover" />

	<Block>
		<div class="text-center">
			<BlockTitle>Welcome to CipherX, {data.user.nickname}! 🎉</BlockTitle>
			<p class="mb-4 text-gray-600">
				Your profile is now complete. Start exploring communities and connecting with like-minded
				people.
			</p>
		</div>
	</Block>

	<Block>
		<Card>
			<div class="p-4">
				<h3 class="mb-2 text-lg font-semibold">Your Profile</h3>
				<div class="space-y-2 text-sm">
					<div><strong>Kink Map Code:</strong> {data.user.kinkMapCode}</div>
					<div><strong>Profile Score:</strong> {data.user.profileCompletenessScore}%</div>
					<div><strong>Points:</strong> {data.user.pointBalance}</div>
					<div><strong>VIP Level:</strong> {data.user.vipLevel}</div>
				</div>
			</div>
		</Card>
	</Block>

	<Block>
		<div class="space-y-3">
			<Button large fill>🔍 Browse Communities</Button>
			<Button large outline>👤 Complete Advanced Profile</Button>
			<Button large outline>🎯 Find Matches</Button>
		</div>
	</Block>

	<Block>
		<div class="text-center text-sm text-gray-500">
			<p>🔒 Your privacy and safety are our top priorities.</p>
			<p>All interactions are encrypted and secure.</p>
		</div>
	</Block>
</Page>
