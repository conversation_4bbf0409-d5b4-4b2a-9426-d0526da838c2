// src/lib/schemas/profile.schema.ts
// CipherX TMA - 用户资料验证规则 (v4.0 - Architected)

import { z } from 'zod';
import {
	// 导入所有需要的原子验证规则
	createIdSchema,
	telegramUserIdSchema,
	tonWalletAddressSchema,
	kinkMapCodeSchema,
	nicknameSchema,
	telegramUsernameSchema,
	ageSchema,
	heightSchema,
	weightSchema,
	countryCodeSchema,
	provinceCodeSchema,
	citySchema,
	languageCodeSchema,
	bioSchema,
	urlSchema,
	kinkRatingsValueSchema
} from '$lib/schemas/common.schema';

// 导入所有需要的“事实来源”
import {
	orientationEnum,
	bodyTypeEnum,
	presentationStyleEnum,
	relationshipStatusEnum
} from '$lib/server/db/schema/_enums';
import { ID_PREFIX_USER } from '$lib/constants/prefiexes';

// --- 1. 数据库模型 Schema (The Database Mirror) ---

/**
 * @description DB User Schema: 与 /server/db/schema/users.ts 中的 `users` 表定义一一对应。
 * 这是从数据库获取数据后的第一层验证标准。
 * 字段名和可空性与数据库完全一致。
 */
export const dbUserSchema = z.object({
	// 核心标识
	id: createIdSchema(ID_PREFIX_USER),
	telegramUserId: telegramUserIdSchema,
	tonWalletAddress: tonWalletAddressSchema.nullable(),
	inviterId: createIdSchema(ID_PREFIX_USER).nullable(),
	kinkMapCode: kinkMapCodeSchema,

	// 基础资料
	nickname: nicknameSchema,
	telegramUsername: telegramUsernameSchema.nullable(),
	age: ageSchema.nullable(),
	heightCm: heightSchema.nullable(),
	weightKg: weightSchema.nullable(),
	countryCode: countryCodeSchema.nullable(),
	provinceCode: provinceCodeSchema.nullable(),
	city: citySchema.nullable(),
	languageCode: languageCodeSchema.nullable(),

	// 高级资料
	bio: bioSchema.nullable(),
	profileImageUrl: urlSchema.nullable(),
	orientation: z.enum(orientationEnum.enumValues).nullable(),
	bodyType: z.enum(bodyTypeEnum.enumValues).nullable(),
	presentationStyle: z.enum(presentationStyleEnum.enumValues).nullable(),
	relationshipStatus: z.enum(relationshipStatusEnum.enumValues).nullable(),

	// Kink 相关数据
	kinkCategoryBitmask: z.bigint().optional().nullable(),
	kinkRatings: z.record(z.string(), kinkRatingsValueSchema).optional().nullable(),

	// 系统计算字段
	profileCompletenessScore: z.number().int(),
	trustScore: z.number().int(),
	vipLevel: z.number().int(),
	pointBalance: z.number().int(),

	// 状态字段
	isActive: z.boolean(),
	isBanned: z.boolean(),

	// 时间戳
	createdAt: z.date(),
	updatedAt: z.date(),
	lastActiveAt: z.date()
});

// --- 2. 业务逻辑模型 Schema (The Business Logic Blueprint) ---

/**
 * @description 用户 Profile 主 Schema：定义了用于业务逻辑（如分数计算）和前端交互的理想形态。
 * 这是我们应用内部处理用户资料的“标准对象”。
 */
export const userProfileSchema = dbUserSchema.pick({
	// 选择所有用户可以自行修改或与个人资料相关的字段
	id: true,
	nickname: true,
	age: true,
	heightCm: true,
	weightKg: true,
	bodyType: true,
	countryCode: true,
	provinceCode: true,
	city: true,
	languageCode: true,
	bio: true,
	orientation: true,
	presentationStyle: true,
	relationshipStatus: true,
	kinkCategoryBitmask: true,
	kinkRatings: true,
	profileImageUrl: true,
	telegramUsername: true,
	profileCompletenessScore: true
});

// --- 3. 上下文“视图” Schema (Contextual "Views") ---

/**
 * @description “编辑用户资料”验证 Schema：
 * 从主业务模型中选择用户可以编辑的字段，并设为可选。
 */
export const updateUserProfileSchema = userProfileSchema.partial();

/**
 * @description Onboarding 表单验证 Schema：
 * 与编辑 Schema 类似，所有字段都是可选的，以支持渐进式填写。
 */
export const onboardingFormSchema = userProfileSchema.partial();

// --- 4. 类型导出 ---

export type DbUser = z.infer<typeof dbUserSchema>;
export type UserProfileData = z.infer<typeof userProfileSchema>;
export type UpdateUserProfileData = z.infer<typeof updateUserProfileSchema>;
export type OnboardingFormData = z.infer<typeof onboardingFormSchema>;