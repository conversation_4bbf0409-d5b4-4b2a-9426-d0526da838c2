/**
 * @file CipherX TMA - 认证相关业务流程验证规则
 * @version 3.0 (Final, camelCase Aligned)
 * @description 本文件定义了与认证相关的、类型安全的数据契约，并与 @telegram-apps/sdk 的 camelCase 输出完全对齐。
 */

import { z } from 'zod';
import {
	createIdSchema,
	telegramUserIdSchema,
	telegramUsernameSchema,
	languageCodeSchema,
	urlSchema // 确保这个已在 common.schema.ts 中定义
} from '$lib/schemas/common.schema';
import { ID_PREFIX_USER } from '$lib/constants/prefiexes';

/**
 * 内部 schema，用于验证从 SDK 的 parse 函数返回的 user 对象。
 * ✅【重大变更】所有字段都已更新为 camelCase，以匹配 SDK 的输出。
 */
const telegramUserSchema = z.object({
	id: telegramUserIdSchema,
	firstName: z.string(), // 对应 first_name
	lastName: z.string().optional(), // 对应 last_name
	username: telegramUsernameSchema.optional(),
	languageCode: languageCodeSchema.optional(), // 对应 language_code
	isPremium: z.boolean().optional(),
	photoUrl: urlSchema.optional() // 对应 photo_url
});

/**
 * 验证从 SDK 的 parse 函数返回的整个 initData 对象。
 * 这是我们后端服务层在调用 parse() 后，进行业务验证的核心。
 */
export const parsedInitDataSchema = z.object({
	hash: z.string().min(1, 'error_field_required'),
	authDate: z.date(), // ✅ SDK 会将 auth_date (number) 转换为 authDate (Date)
	user: telegramUserSchema,
	startParam: z.string().optional(),
	queryId: z.string().optional(),
	chatType: z.string().optional(),
	chatInstance: z.string().optional()
});

/**
 * 这是我们唯一的登录 API 端点 (/api/auth/telegram) 需要验证的请求体。
 * ✅【重大变更】它的职责被简化，只验证原始字符串的存在。
 */
export const loginApiPayloadSchema = z.object({
	initDataRaw: z.string().min(20, { message: 'error_initdata_incomplete' })
});

/**
 * 用于未来邀请码功能的验证 Schema
 */
export const inviteCodeSchema = z.object({
	code: z.string().length(8, 'error_invite_code_length').toUpperCase(),
	inviterId: createIdSchema(ID_PREFIX_USER)
});

// --- 类型导出 ---
// 这些类型将成为我们应用中处理认证数据的标准
export type ParsedInitData = z.infer<typeof parsedInitDataSchema>;
export type LoginApiPayload = z.infer<typeof loginApiPayloadSchema>;
export type TelegramUserFromSDK = z.infer<typeof telegramUserSchema>;
