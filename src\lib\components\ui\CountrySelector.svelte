<!-- src/lib/components/ui/CountrySelector.svelte -->
<script lang="ts">
	import { State, Country } from 'country-state-city';
	import type { ICountry, IState } from 'country-state-city';
	import Svelecte from 'svelecte';

	type Props = {
		countryCode?: string;
		provinceCode?: string;
		initialLanguage?: string;
	};

	let {
		countryCode = $bindable(),
		provinceCode = $bindable(),
		initialLanguage = 'en'
	}: Props = $props();

	let stateSelect: Svelecte;

	const allCountries: ICountry[] = Country.getAllCountries();
	let statesOfCountry: IState[] = [];

	// Reactive statement to update states when countryCode changes
	$effect(() => {
		if (countryCode) {
			statesOfCountry = State.getStatesOfCountry(countryCode);
		} else {
			statesOfCountry = [];
		}
		// Clear province selection when country changes
		provinceCode = undefined;
		stateSelect?.clear();
	});

// Set initial country based on language, if countryCode is not already set
	$effect.pre(() => {
		if (!countryCode && initialLanguage) {
			const country = allCountries.find(
				(c) => c.isoCode.toLowerCase() === initialLanguage.toLowerCase()
			);
			if (country) {
				countryCode = country.isoCode;
			}
		}
	});
</script>

<div class="space-y-4">
	<!-- Country Selector -->
	<div>
		<Svelecte
			options={allCountries}
			labelField="name"
			valueField="isoCode"
			placeholder="Select a country..."
			bind:value={countryCode}
			searchable={true}
		/>
	</div>

	<!-- State/Province Selector -->
	<div>
		<Svelecte
			bind:this={stateSelect}
			options={statesOfCountry}
			labelField="name"
			valueField="isoCode"
			placeholder="Select a state/province..."
			bind:value={provinceCode}
			disabled={statesOfCountry.length === 0}
			searchable={true}
		/>
	</div>
</div>
