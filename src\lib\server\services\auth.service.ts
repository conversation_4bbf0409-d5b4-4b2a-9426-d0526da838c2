/**
 * @file CipherX TMA - 核心认证服务
 * @version 2.1 (Final)
 * @description 提供一个全应用统一的、安全的 Telegram initData 验证与解析服务。
 */

import { BOT_TOKEN } from '$env/static/private';
import { validate, parse, type InitData, ExpiredError } from '@telegram-apps/init-data-node';
import { parsedInitDataSchema } from '$lib/schemas/auth.schema';
import type { ParsedInitData } from '$lib/schemas/auth.schema';

// 定义我们自己的、清晰的错误码
export type ValidationError =
	| 'INVALID_HASH'
	| 'AUTH_DATE_TOO_OLD'
	| 'INVALID_DATA_FORMAT'
	| 'MISSING_BOT_TOKEN';

// 定义服务返回的统一结果类型
type ValidationResult =
	| { success: true; data: ParsedInitData }
	| { success: false; error: ValidationError };

// 将容忍时间差定义为常量，便于管理
const AUTH_DATE_TOLERANCE_SECONDS = 3600; // 容忍1小时内的 initData

/**
 * 安全地验证、解析并校验来自 Telegram Mini App 的 initData 字符串。
 * 这是我们整个认证流程中，最核心的安全函数。
 * @param initDataRaw - 原始的 initData 字符串。
 * @returns 包含成功数据或失败错误码的结果对象。
 */
export function validateAndParseInitData(initDataRaw: string): ValidationResult {
	if (!BOT_TOKEN) {
		console.error('FATAL: TELEGRAM_BOT_TOKEN is not configured in .env file.');
		return { success: false, error: 'MISSING_BOT_TOKEN' };
	}

	try {
		// 1. 使用官方库进行签名和时效性校验
		validate(initDataRaw, BOT_TOKEN, {
			expiresIn: AUTH_DATE_TOLERANCE_SECONDS
		});

		// 2. 校验通过后，使用官方库将其解析为 camelCase 对象
		const parsedDataFromSDK = parse(initDataRaw);

		// 3. 使用我们自己的 Zod schema 对解析后的对象进行最终的结构和类型验证
		const validationResult = parsedInitDataSchema.safeParse(parsedDataFromSDK);

		if (!validationResult.success) {
			// 如果 Zod 验证失败，说明数据结构不符合我们的业务预期
			console.error('Zod validation failed after parsing initData:', validationResult.error);
			return { success: false, error: 'INVALID_DATA_FORMAT' };
		}

		// 4. 所有验证通过，返回干净、安全、类型化的数据
		return { success: true, data: validationResult.data };
	} catch (e: unknown) {
		console.error('Error during initData validation:', e);

		// 使用 instanceof 捕获特定的错误类型
		if (e instanceof ExpiredError) {
			return { success: false, error: 'AUTH_DATE_TOO_OLD' };
		}

		// 对于其他所有异常（包括哈希无效），都归为一种类型
		return { success: false, error: 'INVALID_HASH' };
	}
}

