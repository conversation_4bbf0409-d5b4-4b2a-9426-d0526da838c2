// src/lib/server/db/schema/economy.ts
// CipherX TMA - 经济系统相关表定义

import { pgTable, uuid, text, integer, timestamp } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { pointTransactionTypeEnum } from './_enums';
import { users } from './users';

/**
 * 积分交易表 - 经济系统的流水账本
 * 以不可变的方式，记录所有积分的获得和消耗
 */
export const pointTransactions = pgTable('point_transactions', {
	// 这个表的ID与其他业务无关，使用UUID是很好的选择
	id: uuid('id').primaryKey().defaultRandom(),

	// 关键修正点 1: 添加外键约束
	userId: text('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),

	amount: integer('amount').notNull(),
	type: pointTransactionTypeEnum('type').notNull(),
	description: text('description'),

	// referenceId 用于关联触发本次交易的实体，比如一次“超级喜欢”的 match 记录
	referenceId: text('reference_id'),

	createdAt: timestamp('created_at', { withTimezone: true }).notNull().defaultNow()
});

// --- 关系定义 (Relations) ---
// 关键修正点 2: 将 relations 定义移到本文件中
export const pointTransactionsRelations = relations(pointTransactions, ({ one }) => ({
	// 定义每条交易记录都属于一个用户
	user: one(users, {
		fields: [pointTransactions.userId],
		references: [users.id]
	})
}));

// --- 类型导出 ---
export type PointTransaction = typeof pointTransactions.$inferSelect;
export type NewPointTransaction = typeof pointTransactions.$inferInsert;
