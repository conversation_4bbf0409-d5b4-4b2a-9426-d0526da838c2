/**
 * @file Root Page - Server Actions
 * @description 处理应用的自动登录流程。
 */

import { fail, redirect } from '@sveltejs/kit';
import type { Actions } from './$types';
import { lucia } from '$lib/server/auth';

// 导入我们已经设计好的、专业的服务模块
import { validateAndParseInitData } from '$lib/server/services/auth.service';
import { findOrCreateUser } from '$lib/server/services/user.service';
import { isInitialOnboardingRequired } from '$lib/utils/user.utils';

export const actions: Actions = {
	// 这个 'login' action 将由根页面的隐藏表单自动调用
	login: async ({ request, cookies }) => {
		const formData = await request.formData();
		const initDataRaw = formData.get('initData') as string;

		if (!initDataRaw) {
			return fail(400, { message: 'initData is missing' });
		}

		// 1. 调用核心安全服务，完成所有 initData 的校验
		const validationResult = validateAndParseInitData(initDataRaw);
		if (!validationResult.success) {
			return fail(403, { message: `Authentication failed: ${validationResult.error}` });
		}

		const { user: telegramUser, startParam } = validationResult.data;
		if (!telegramUser) {
			return fail(400, { message: 'User data is missing in initData.' });
		}

		try {
			// 2. 调用用户服务，查找或创建用户，并传递邀请人信息
			const userInDb = await findOrCreateUser(telegramUser, startParam);

			// 3. 使用 Lucia 创建会话并设置安全的 HttpOnly Cookie
			const session = await lucia.createSession(userInDb.id, {});
			const sessionCookie = lucia.createSessionCookie(session.id);
			cookies.set(sessionCookie.name, sessionCookie.value, {
				path: '.',
				...sessionCookie.attributes
			});

			// 4. 使用可复用的工具函数，判断用户下一步应该去哪里
			const userNeedsOnboarding = isInitialOnboardingRequired(userInDb);

			// 5. 使用 throw redirect 在 Action 成功后进行跳转
			throw redirect(303, userNeedsOnboarding ? '/onboarding' : '/discover');
		} catch (e) {
			console.error('Login action failed during user processing:', e);
			return fail(500, { message: 'Internal server error.' });
		}
	}
};
