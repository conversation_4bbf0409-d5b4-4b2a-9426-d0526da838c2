<script lang="ts">
	import { onMount, tick } from 'svelte';
	import { getInitDataRaw } from '$lib/telegram/tma.service';
	import { Preloader, Page } from 'konsta/svelte';
	import * as m from '$lib/paraglide/messages';

	let formElement: HTMLFormElement;
	let initDataString = $state('');
	let error = $state<string | null>(null); // 用于处理无法获取initData的错误

	onMount(() => {
		const data = getInitDataRaw();
		if (data) {
			initDataString = data;
			// 使用 tick() 确保 Svelte 完成了 DOM 更新，input 的 value 已经写入
			tick().then(() => {
				formElement?.requestSubmit();
			});
		} else {
			// 如果在 onMount 时依然没有 initData，说明环境异常
			error = m.error_initdata_not_found(); // 假设在 messages 中定义了此错误
		}
	});
</script>

<Page>
	<!-- 这个表单是隐藏的，由JS自动提交到当前页面的 login action -->
	<form action="?/login" method="POST" bind:this={formElement} class="hidden">
		<input type="hidden" name="initData" bind:value={initDataString} />
	</form>

	<!-- 页面始终显示加载或错误状态，因为成功后会被服务器重定向走 -->
	<div class="flex h-screen items-center justify-center p-4">
		{#if error}
			<div class="text-center text-red-500">{error}</div>
		{:else}
			<div class="text-center text-gray-500">
				<Preloader size="w-12 h-12" />
				<p class="mt-4">{m.authenticating_message()}</p>
			</div>
		{/if}
	</div>
</Page>
