// src/lib/server/services/user.service.ts
// 职责：封装所有与 users 表相关的数据库交互逻辑。 (v2.0 - Architected)

import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import {
	dbUserSchema,
	type UpdateUserProfileData,
	type UserProfileData
} from '$lib/schemas/profile.schema';
import type { TelegramUserFromSDK } from '$lib/schemas/auth.schema';
import { eq } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { calculateProfileCompleteness } from '$lib/utils/user.utils';
import { ID_PREFIX_INVITE_CODE, ID_PREFIX_USER } from '$lib/constants/prefiexes';

/**
 * 根据Telegram用户数据查找或创建用户，并更新活跃状态。
 * @param telegramUser - 经过验证的、来自SDK的 camelCase 用户对象。
 * @param startParam - (可选) 从 initData 中解析出的启动参数，可能包含邀请者ID。
 * @returns 数据库中完整、最新的用户对象。
 */
export async function findOrCreateUser(telegramUser: TelegramUserFromSDK, startParam?: string) {
	const existingUser = await db.query.users.findFirst({
		where: eq(users.telegramUserId, telegramUser.id)
	});

	const inviterId = startParam?.startsWith(ID_PREFIX_INVITE_CODE)
		? startParam.replace(ID_PREFIX_INVITE_CODE, '')
		: undefined;

	if (existingUser) {
		console.log(`[UserService] Found existing user: ${existingUser.id}`);
		const [updatedUser] = await db
			.update(users)
			.set({
				telegramUsername: telegramUser.username,
				profileImageUrl: telegramUser.photoUrl,
				lastActiveAt: new Date()
			})
			.where(eq(users.id, existingUser.id))
			.returning();
		return updatedUser;
	} else {
		console.log(`[UserService] Creating new user for Telegram ID: ${telegramUser.id}`);
		const userId: string = ID_PREFIX_USER + `${nanoid(15)}`;
		const kinkMapCode = nanoid(8).toUpperCase();

		const profileForScore: Partial<UserProfileData> = {
			id: userId,
			nickname: telegramUser.firstName || `User ${telegramUser.id}`,
			languageCode: telegramUser.languageCode
		};
		const initialScore = calculateProfileCompleteness(profileForScore);

		const [newUser] = await db
			.insert(users)
			.values({
				id: userId,
				telegramUserId: telegramUser.id,
				kinkMapCode: kinkMapCode,
				nickname: profileForScore.nickname,
				telegramUsername: telegramUser.username,
				languageCode: profileForScore.languageCode,
				profileImageUrl: telegramUser.photoUrl,
				inviterId: inviterId,
				profileCompletenessScore: initialScore
			})
			.returning();

		return newUser;
	}
}

/**
 * 更新用户的个人资料，并同步更新资料完整度分数。
 * @param userId - 要更新的用户的应用内 ID。
 * @param data - 经过 Zod 验证的、安全的待更新数据。
 */
export async function updateUserProfile(userId: string, data: UpdateUserProfileData) {
	try {
		const currentUserData = await db.query.users.findFirst({
			where: eq(users.id, userId)
		});

		if (!currentUserData) {
			throw new Error('error_user_not_found');
		}

		const validatedDbUser = dbUserSchema.parse(currentUserData);

		const profileForScore: UserProfileData = {
			...validatedDbUser,
			...data
		};

		const newScore = calculateProfileCompleteness(profileForScore);

		await db
			.update(users)
			.set({
				...data,
				profileCompletenessScore: newScore,
				updatedAt: new Date()
			})
			.where(eq(users.id, userId));

		return { success: true };
	} catch (error) {
		console.error(`[UserService] Failed to update profile for user ${userId}:`, error);
		throw new Error('error_database_update_failed');
	}
}

/**
 * 获取一个用户的完整公开资料，用于展示给其他用户。
 * @param userId - 要查询的用户的应用内ID。
 */
export async function getFullUserProfile(userId: string) {
	// 使用 Drizzle 的 query API，并明确只选择我们定义为“公开”的列
	const userProfile = await db.query.users.findFirst({
		where: eq(users.id, userId),
		columns: {
			// 核心标识
			id: true,
			kinkMapCode: true,
			// 公开资料
			nickname: true,
			age: true,
			heightCm: true,
			weightKg: true,
			countryCode: true,
			provinceCode: true,
			city: true,
			bio: true,
			profileImageUrl: true,
			// 高级资料
			orientation: true,
			bodyType: true,
			presentationStyle: true,
			relationshipStatus: true,
			// Kink 数据
			kinkCategoryBitmask: true,
			kinkRatings: true,
			// 状态与审计
			trustScore: true,
			createdAt: true,
			lastActiveAt: true, // 返回精确时间，由前端负责“模糊化”处理
			profileCompletenessScore: true // ✅ 确保获取 profileCompletenessScore
		}
	});

	return userProfile ?? null;
}
