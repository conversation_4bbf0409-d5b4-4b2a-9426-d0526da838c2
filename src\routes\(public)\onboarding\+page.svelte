<!-- src/routes/(public)/onboarding/+page.svelte -->
<script lang="ts">
	import {
		Page,
		Navbar,
		Block,
		BlockTitle,
		List,
		ListInput,
		Button,
		ListItem
	} from 'konsta/svelte';
	import * as m from '$lib/paraglide/messages';
	import type { PageData } from './$types';
	import ChipGroup from '$lib/components/ui/ChipGroup.svelte';
	import RangeSlider from '$lib/components/ui/RangeSlider.svelte';
	import CountrySelector from '$lib/components/ui/CountrySelector.svelte';
	import { bodyTypeEnum } from '$lib/server/db/schema';

	export let data: PageData;

	const { form, errors, submitting, languageCode } = data;

	// Prepare items for the ChipGroup
	const bodyTypeItems = bodyTypeEnum.enumValues.map((value) => ({
		value,
		label: m[`body_type_${value}`]() // Assumes you have messages like body_type_male_body(), etc.
	}));
</script>

<Page>
	<Navbar title={m.onboarding_title()} />

	<form method="POST">
		<BlockTitle>{m.onboarding_basic_profile_title()}</BlockTitle>
		<Block strong inset>
			<p class="text-sm text-gray-500 mb-4">{m.onboarding_basic_profile_description()}</p>

			<List strong inset>
				<!-- Nickname -->
				<ListInput
					label={m.onboarding_label_nickname()}
					type="text"
					placeholder={m.onboarding_placeholder_nickname()}
					name="nickname"
					bind:value={$form.nickname}
					error={$errors.nickname ? $errors.nickname[0] : ''}
					errorMessage={$errors.nickname ? $errors.nickname[0] : ''}
				/>
			</List>
		</Block>

		<BlockTitle>{m.onboarding_physical_chars_title()}</BlockTitle>
		<List strong inset>
			<!-- Body Type -->
			<ListItem
				title={m.onboarding_label_body_type()}
				text={$form.bodyType ? m[`body_type_${$form.bodyType}`]() : m.common_please_select()}
			/>
			<div class="p-4 pt-2">
				<ChipGroup items={bodyTypeItems} bind:value={$form.bodyType} />
				{#if $errors.bodyType}
					<p class="text-red-500 text-sm mt-2">{$errors.bodyType[0]}</p>
				{/if}
			</div>

			<!-- Age -->
			<RangeSlider
				label={m.onboarding_label_age()}
				min={18}
				max={99}
				step={1}
				bind:value={$form.age}
			/>

			<!-- Height (cm) -->
			<RangeSlider
				label={m.onboarding_label_height()}
				min={140}
				max={220}
				step={1}
				unit="cm"
				bind:value={$form.heightCm}
			/>

			<!-- Weight (kg) -->
			<RangeSlider
				label={m.onboarding_label_weight()}
				min={40}
				max={150}
				step={1}
				unit="kg"
				bind:value={$form.weightKg}
			/>
		</List>

		<BlockTitle>{m.onboarding_location_title()}</BlockTitle>
		<div class="p-4">
			<CountrySelector
				bind:countryCode={$form.countryCode}
				bind:provinceCode={$form.provinceCode}
				initialLanguage={languageCode}
			/>
		</div>
		<List strong inset>
			<!-- City -->
			<ListInput
				label={m.onboarding_label_city()}
				type="text"
				placeholder={m.onboarding_placeholder_city()}
				name="city"
				bind:value={$form.city}
				error={$errors.city ? $errors.city[0] : ''}
				errorMessage={$errors.city ? $errors.city[0] : ''}
			/>
		</List>

		<Block class="p-4">
			<Button large tonal type="submit" disabled={$submitting}>
				{#if $submitting}
					{m.common_saving()}
				{:else}
					{m.common_save_and_continue()}
				{/if}
			</Button>
		</Block>
	</form>
</Page>
