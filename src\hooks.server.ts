import { sequence } from '@sveltejs/kit/hooks';
import { lucia } from '$lib/server/auth';
import type { Handle } from '@sveltejs/kit';
import { paraglideMiddleware } from '$lib/paraglide/server';

const handleParaglide: Handle = ({ event, resolve }) =>
	paraglideMiddleware(event.request, ({ request, locale }) => {
		event.request = request;

		return resolve(event, {
			transformPageChunk: ({ html }) => html.replace('%paraglide.lang%', locale)
		});
	});

const handleAuth: Handle = async ({ event, resolve }) => {
	const sessionId = event.cookies.get(lucia.sessionCookieName);

	if (!sessionId) {
		event.locals.user = null;
		event.locals.session = null;
		return resolve(event);
	}

	const { session, user } = await lucia.validateSession(sessionId);

	if (session && session.fresh) {
		const sessionCookie = lucia.createSessionCookie(session.id);
		event.cookies.set(sessionCookie.name, sessionCookie.value, {
			path: '.',
			...sessionCookie.attributes
		});
	}

	if (!session) {
		const sessionCookie = lucia.createBlankSessionCookie();
		event.cookies.set(sessionCookie.name, sessionCookie.value, {
			path: '.',
			...sessionCookie.attributes
		});
	}

	event.locals.user = user;
	event.locals.session = session;
	return resolve(event);
};

// src/hooks.server.ts (追加)
import type { HandleServerError } from '@sveltejs/kit';

export const handleError: HandleServerError = ({ error, event }) => {
	// 将错误信息记录到您的监控服务
	console.error('An unexpected error occurred:', {
		error,
		user: event.locals.user?.id,
		route: event.route.id
	});
	// Sentry.captureException(error, { extra: { ... } });

	// 可以向用户返回一个不包含敏感信息的、通用的错误消息
	return {
		message: '服务器开小差了，请稍后再试 T_T',
		code: 'INTERNAL_ERROR'
	};
};

export const handle: Handle = sequence(handleParaglide, handleAuth);
