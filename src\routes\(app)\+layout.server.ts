// src/routes/(app)/+layout.server.ts
// CipherX TMA - 应用内页面的路由保护

import { redirect } from '@sveltejs/kit';
import type { LayoutServerLoad } from './$types';

export const load: ServerLoad = async ({ locals, url }: { locals: App.Locals; url: URL }) => {
	// 检查用户是否已登录
	if (!locals.user) {
		throw redirect(303, '/');
	}

	// 检查用户是否完成了基础onboarding
	const needsOnboarding =
		!locals.user.age || !locals.user.heightCm || !locals.user.weightKg || !locals.user.bodyType;

	// 如果用户需要完成onboarding且不在onboarding页面，重定向到onboarding
	if (needsOnboarding && !url.pathname.startsWith('/onboarding')) {
		throw redirect(303, '/onboarding');
	}

	// 如果用户已完成onboarding但在onboarding页面，重定向到discover
	if (!needsOnboarding && url.pathname.startsWith('/onboarding')) {
		throw redirect(303, '/discover');
	}

	return {
		user: {
			id: locals.user.id,
			nickname: locals.user.nickname,
			kinkMapCode: locals.user.kinkMapCode,
			profileCompletenessScore: locals.user.profileCompletenessScore,
			pointBalance: locals.user.pointBalance,
			vipLevel: locals.user.vipLevel
		}
	};
};
