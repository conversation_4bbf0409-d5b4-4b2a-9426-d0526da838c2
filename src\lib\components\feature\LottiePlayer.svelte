<script lang="ts">
	import { onMount } from 'svelte';
	import { DotLottie, type Config } from '@lottiefiles/dotlottie-web';

	// --- 1. 类型推导 (解决未导出类型的问题) ---
	type DotLottieInstance = InstanceType<typeof DotLottie>;
	type MarkerType = Parameters<DotLottieInstance['setMarker']>[0];
	type LayoutType = Parameters<DotLottieInstance['setLayout']>[0];
	type SegmentType =
		| Parameters<DotLottieInstance['setSegment']>[0]
		| Parameters<DotLottieInstance['setSegment']>[1];

	// --- 2. 定义所有 Props ---
	let {
		src,
		data,
		autoplay = false,
		loop = false,
		speed = 1,
		mode = 'forward',
		marker,
		layout,
		animationId = '',
		themeId = '',
		themeData = '',
		backgroundColor,
		segment,
		useFrameInterpolation = true,
		playOnHover = false,
		dotLottieRefCallback = () => {}
	}: {
		src?: Config['src'];
		data?: Config['data'];
		autoplay?: boolean;
		loop?: boolean;
		speed?: number;
		mode?: 'forward' | 'reverse';
		marker?: MarkerType;
		layout?: LayoutType;
		animationId?: string;
		themeId?: string;
		themeData?: string;
		backgroundColor?: string;
		segment?: [SegmentType, SegmentType];
		useFrameInterpolation?: boolean;
		playOnHover?: boolean;
		dotLottieRefCallback?: (dotLottie: DotLottie) => void;
	} = $props();

	let dotLottie: DotLottie;
	let canvas: HTMLCanvasElement;

	// --- 3. 初始化与事件监听 (onMount) ---
	onMount(() => {
		dotLottie = new DotLottie({
			canvas,
			autoplay: autoplay && !playOnHover,
			loop,
			speed,
			mode,
			src,
			data,
			animationId,
			themeId,
			backgroundColor,
			useFrameInterpolation
		});

		// 将 dotLottie 实例回调给父组件
		if (dotLottieRefCallback) {
			dotLottieRefCallback(dotLottie);
		}

		// 处理鼠标悬停播放
		const hoverHandler = (event: MouseEvent) => {
			if (!playOnHover || !dotLottie.isLoaded) return;
			event.type === 'mouseenter' ? dotLottie.play() : dotLottie.stop();
		};
		canvas.addEventListener('mouseenter', hoverHandler);
		canvas.addEventListener('mouseleave', hoverHandler);

		// 在组件销毁时，清理实例和事件监听
		return () => {
			canvas.removeEventListener('mouseenter', hoverHandler);
			canvas.removeEventListener('mouseleave', hoverHandler);
			dotLottie?.destroy();
		};
	});

	// --- 4. 响应式更新 ($effect) ---

	// 这个 effect 负责重新加载动画源
	$effect(() => {
		if (!dotLottie) return;
		// 追踪 src 和 data 的变化
		const _src = src;
		const _data = data;
		dotLottie.load({ src, data });
	});

	// 这个 effect 负责处理所有可实时更新的属性
	$effect(() => {
		if (!dotLottie?.isLoaded) return;

		// 追踪其他所有控制属性的变化
		if (dotLottie.speed !== speed) dotLottie.setSpeed(speed);
		if (dotLottie.loop !== loop) dotLottie.setLoop(loop);
		if (dotLottie.mode !== mode) dotLottie.setMode(mode);
		if (dotLottie.useFrameInterpolation !== useFrameInterpolation)
			dotLottie.setUseFrameInterpolation(useFrameInterpolation);
		if (layout) dotLottie.setLayout(layout);
		if (marker) dotLottie.setMarker(marker);
		if (segment) dotLottie.setSegment(segment[0], segment[1]);
		if (dotLottie.activeAnimationId !== animationId) dotLottie.loadAnimation(animationId);
		if (dotLottie.activeThemeId !== themeId) dotLottie.setTheme(themeId);
		if (themeData) dotLottie.setThemeData(themeData);
	});

	// 单独处理背景色，因为它不需要 isLoaded
	$effect(() => {
		if (dotLottie) dotLottie.setBackgroundColor(backgroundColor || 'transparent');
	});
</script>

<div style="width: 100%; height: 100%;">
	<canvas bind:this={canvas} style="width: 100%; height: 100%; display: block;"></canvas>
</div>
