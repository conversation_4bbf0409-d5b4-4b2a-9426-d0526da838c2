// src/lib/telegram/tma.service.ts
/**
 * @file CipherX TMA - Telegram Mini App SDK 服务
 * @version 3.2 (Final)
 * @description 本文件是应用与 Telegram SDK 交互的唯一、统一的入口点。
 * 它负责初始化SDK，并重新导出其核心的、响应式的组件控制器。
 */

import {
	init,
	retrieveLaunchParams,
	// 直接导入并重新导出官方提供的、小写字母开头的响应式“信号”
	mainButton,
	backButton,
	viewport,
	hapticFeedback,
	themeParams,
	initData
} from '@telegram-apps/sdk-svelte';

import { writable } from 'svelte/store';
import { dev } from '$app/environment';

// --- 重新导出官方的信号，供整个应用使用 ---
export { mainButton, backButton, viewport, hapticFeedback, themeParams, initData };

/**
 * 一个简单的布尔值 store，用于在 UI 中轻松判断 SDK 是否已就绪。
 */
export const tmaReady = writable<boolean>(false);

let isInitialized = false;

/**
 * 唯一的TMA SDK初始化入口函数。
 * 应在根布局 `+layout.svelte` 的 onMount 中被调用一次。
 * @returns 返回一个清理函数，用于在组件销毁时调用。
 */
export function initializeTma(): () => void {
	if (typeof window === 'undefined' || isInitialized) {
		return () => {};
	}
	isInitialized = true;

	try {
		// ✅ 最终、正确的调用方式
		// 我们传入 acceptCustomStyles 来确保主题可以响应式更新
		const cleanup = init({
			acceptCustomStyles: true
			// checkOnInit 在新的 InitOptions 中可能不存在，因此移除以保证兼容性
		});

		// 默认展开视口以获得最佳体验
		viewport.expand();

		tmaReady.set(true);
		console.log('✅ Telegram Mini App SDK initialized correctly.');

		return cleanup;
	} catch (e: unknown) {
		const error = e instanceof Error ? e : new Error(String(e));
		console.error('❌ Failed to initialize TMA SDK:', error);
		tmaReady.set(true);
		return () => {};
	}
}

/**
 * 一个安全的、非响应式的函数，用于在特定时刻获取原始的 initData 字符串。
 * 主要用于在应用启动时，提交给后端的 Form Action。
 */
export function getInitDataRaw(): string | undefined {
	try {
		const params = retrieveLaunchParams();
		const rawData = params.initDataRaw;

		if (typeof rawData === 'string' && rawData.length > 0) {
			return rawData;
		}
		return undefined;
	} catch (e) {
		if (dev) {
			console.log('Could not retrieve launch params. This is expected in a local browser.');
		}
		return undefined;
	}
}
