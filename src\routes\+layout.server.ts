// src/routes/+layout.server.ts
// CipherX TMA - 全局服务端数据加载

import type { LayoutServerLoad } from './$types';
// 推荐：将业务逻辑封装到工具函数中
import { isInitialOnboardingRequired } from '$lib/utils/user.utils';

export const load: LayoutServerLoad = async ({ locals }) => {
	// 这个 locals.user 是由 hooks.server.ts 中 Lucia 验证后提供的
	const { user } = locals;

	// 返回一个安全处理过的、包含前端所需所有全局信息的用户对象
	return {
		user: user
			? {
					id: user.id,
					nickname: user.nickname,
					kinkMapCode: user.kinkMapCode,
					// 调用工具函数来判断，让 load 函数保持简洁
					isInitialOnboardingRequired: isInitialOnboardingRequired(user)
				}
			: null
	};
};
