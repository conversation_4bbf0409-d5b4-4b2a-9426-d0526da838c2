/**
 * CipherX Kink Data Constitution (v1.0)
 * * 本文件是项目中关于 Kink 偏好数据的“唯一事实来源 (Single Source of Truth)”。
 * 它定义了所有合法的角色和兴趣类别。
 * 所有相关的代码（包括由AI生成），都必须严格导入并遵循本文件中的定义。
 * 绝对禁止在任何其他地方硬编码或发明新的 Kink 类型。
 */

// --- 1. 核心角色 (用于 bitmask) ---

/**
 * 定义了用户的核心角色身份。
 * 值使用位移运算，确保每个角色都是一个独立的标志位。
 * 复合角色（如 Switch）是基础角色的“按位或”(|)组合。
 * 这提供了完美的“用户身份认同”与“数据查询效率”的平衡。
 */

export const KINK_CATEGORY_BITMASK = {
	DOMINANT: 1 << 0,
	SUBMISSIVE: 1 << 1,
	SWITCH_DS: (1 << 0) | (1 << 1),
	TOP: 1 << 2,
	BOTTOM: 1 << 3,
	VERSATILE: (1 << 2) | (1 << 3),
	SIDE: 1 << 4,
	EDGEE: 1 << 5,
	EDGER: 1 << 6,
	EDGE_SWITCHER: (1 << 5) | (1 << 6)
} as const;

export type KinkCategoryBitmask = keyof typeof KINK_CATEGORY_BITMASK;

// --- 2. 具体兴趣偏好 (用于 JSONB 评分) ---
export const KINK_RATINGS = [
	// 身体感知与互动
	'sight',
	'sound',
	'smell',
	'mouth',
	'nipple_play',
	'hand_play',
	'chastity',
	'genital_play',
	'buttock_play',
	'anal_play',
	'foot_fetish',

	// 场景与形式
	'service',
	'role_playing',
	'public_play',
	'online',
	'tool_play'
] as const;

export type KinkRatings = (typeof KINK_RATINGS)[number];

// --- 3. 评分值定义 ---
export type KinkRatingValue = -2 | -1 | 0 | 1 | 2 | 3 | 4 | 5;
