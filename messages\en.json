{"$schema": "https://inlang.com/schema/inlang-message-format", "hello_world": "Hello, {name} from en!", "app_title": "CipherX", "kink_role_DOMINANT": "Dominant", "kink_role_SUBMISSIVE": "Submissive", "kink_role_SWITCH_DS": "Switch (D/s)", "kink_role_TOP": "Top", "kink_role_BOTTOM": "Bottom", "kink_role_VERSATILE": "Versatile", "kink_role_SIDE": "Side", "kink_role_EDGEE": "<PERSON><PERSON>", "kink_role_EDGER": "<PERSON><PERSON>", "kink_role_EDGE_SWITCHER": "Switch (Edge)", "kink_interest_sight": "Visual", "kink_interest_sound": "Auditory", "kink_interest_smell": "Olfactory", "kink_interest_mouth": "Oral", "kink_interest_nipple_play": "Nipple Play", "kink_interest_hand_play": "Hand Play", "kink_interest_chastity": "Chastity", "kink_interest_genital_play": "Genital Play", "kink_interest_buttock_play": "Buttock Play", "kink_interest_anal_play": "Anal Play", "kink_interest_foot_fetish": "<PERSON>", "kink_interest_service": "Service", "kink_interest_role_playing": "Role Playing", "kink_interest_public_play": "Public Play", "kink_interest_online": "Online / Remote", "kink_interest_tool_play": "Tool / Toy Play", "kink_rating_label_-2": "Obediently if required", "kink_rating_label_-1": "Hard Limit / No", "kink_rating_label_0": "Neutral / Indifferent", "kink_rating_label_1": "S<PERSON>ly <PERSON>ous", "kink_rating_label_2": "Interested", "kink_rating_label_3": "Like It", "kink_rating_label_4": "Love It", "kink_rating_label_5": "Favorite / Essential", "group_theme_men-seeking-men": "Rainbow Men", "group_theme_women-seeking-women": "Rainbow Women", "group_theme_all-inclusive-community": "CipherX Community", "error_id_required": "ID is required.", "error_id_prefix_invalid": "Invalid ID format.", "error_telegram_id_required": "Telegram User ID is required.", "error_telegram_id_invalid": "Invalid Telegram User ID.", "error_kink_map_code_required": "Kink Map Code is required.", "error_kink_map_code_length": "Kink Map Code must be 8 characters long.", "error_nickname_required": "Nickname is required.", "error_nickname_too_short": "Nickname must be at least 2 characters.", "error_nickname_too_long": "Nickname cannot exceed 50 characters.", "error_telegram_username_format": "Username can only contain letters, numbers, and underscores.", "error_bio_too_long": "Bio cannot exceed 500 characters.", "error_age_must_be_number": "Age must be a number.", "error_age_must_be_integer": "Age must be a whole number.", "error_age_too_young": "You must be at least 18 years old.", "error_age_invalid": "Please enter a valid age.", "error_height_must_be_number": "Height must be a number.", "error_height_out_of_range": "Please enter a valid height (100-250 cm).", "error_weight_must_be_number": "Weight must be a number.", "error_weight_out_of_range": "Please enter a valid weight (30-300 kg).", "error_country_code_invalid": "Invalid country code.", "error_city_required": "City name is required.", "error_ton_address_invalid": "Invalid TON wallet address format.", "error_rating_invalid": "Invalid rating value.", "error_slug_format": "Slug can only contain lowercase letters, numbers, and hyphens.", "error_slug_no_hyphen_ends": "Slug cannot start or end with a hyphen.", "error_slug_too_short": "Slug must be at least 3 characters long.", "error_slug_too_long": "Slug cannot exceed 100 characters.", "error_invitee_identifier_required": "Either a user ID or a Telegram user ID for the invitee must be provided.", "error_initdata_incomplete": "Authentication data is incomplete. Key fields are missing.", "error_initdata_parse_failed": "Authentication data is malformed and cannot be parsed.", "error_invite_code_length": "Invite code must be exactly 8 characters long.", "onboarding_title": "Complete Your Profile", "onboarding_description": "To provide you with the best experience, we need to know a little bit about you. This information will be used to personalize your journey.", "nickname_label": "Nickname", "age_label": "Age", "height_label": "Height (cm)", "weight_label": "Weight (kg)", "body_type_label": "Body Type", "select_body_type_placeholder": "Select your body type...", "country_label": "Country", "province_label": "Province / State", "city_label": "City", "onboarding_submit_button": "Save and Continue", "body_type_male_body": "Male Body", "body_type_female_body": "Female Body", "body_type_other_body_type": "Other", "onboarding_accept_privacy_terms": "I accept the Privacy Policy and Terms of Service", "onboarding_show_advanced_profile": "Show Advanced Profile Options", "advanced_profile_title": "Advanced Profile", "orientation_label": "Sexual Orientation", "orientation_straight": "Straight", "orientation_gay": "<PERSON>", "orientation_lesbian": "Lesbian", "orientation_bisexual": "Bisexual", "orientation_asexual": "Asexual", "orientation_demisexual": "Demisexual", "orientation_pansexual": "Pansexual", "orientation_queer": "Queer", "orientation_fluid": "Fluid", "orientation_other_orientation": "Other", "orientation_prefer_not_to_say_orientation": "Prefer Not to Say", "presentation_style_label": "Presentation Style", "presentation_style_conventional_masculine": "Conventional Masculine", "presentation_style_rugged_masculine": "Rugged Mas<PERSON>line", "presentation_style_feminine": "Feminine", "presentation_style_androgynous_neutral": "Androgynous / Neutral", "presentation_style_other_presentation_style": "Other", "relationship_status_label": "Relationship Status", "relationship_status_single": "Single", "relationship_status_in_a_relationship": "In a Relationship", "relationship_status_complicated": "Complicated", "relationship_status_open_relationship": "Open Relationship", "relationship_status_married": "Married", "relationship_status_polyamorous": "<PERSON><PERSON><PERSON>", "relationship_status_other_relationship_status": "Other", "relationship_status_prefer_not_to_say_relationship_status": "Prefer Not to Say", "bio_label": "Bio", "bio_placeholder": "Tell us about yourself...", "kink_roles_label": "<PERSON><PERSON>s", "kink_interests_label": "<PERSON><PERSON>s"}